[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --html=reports/report.html
    --self-contained-html
    --browser chromium
    --headed
markers =
    smoke: marks tests as smoke tests
    regression: marks tests as regression tests
    login: marks tests related to login functionality
    ui: marks tests for UI validation
    slow: marks tests as slow running
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
